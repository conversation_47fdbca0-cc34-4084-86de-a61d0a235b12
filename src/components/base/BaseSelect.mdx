import { Canvas, Meta, ArgTypes, Source } from '@storybook/docs/blocks'
import * as BaseSelectStories from './BaseSelect.stories'

<Meta of={BaseSelectStories} />

# BaseSelect

The `BaseSelect` component is a foundational form element that provides a consistent, accessible, and customizable dropdown selection interface across your application.

## Overview

<Canvas of={BaseSelectStories.Default} />

## Key Features

### 🎯 **Accessibility First**

- Semantic HTML with proper `<select>` element
- Full keyboard navigation support
- Screen reader compatible
- ARIA attributes for enhanced accessibility

### 🎨 **Design System Integration**

- Uses CSS custom properties for consistent theming
- Responsive design with mobile-first approach
- Consistent with other Base components

### ⚡ **Developer Experience**

- TypeScript support with proper type definitions
- Vue 3 Composition API
- v-model support for two-way data binding
- Comprehensive prop validation

### 🔧 **Flexible Configuration**

- Optional clear functionality
- Custom validation and error states
- Support for various data types (string, number, object)
- Configurable labels and placeholders

## Basic Usage

```vue
<script setup>
import { ref } from 'vue'

const selectedFruit = ref('')
const fruits = [
  { label: 'Apple', value: 'apple' },
  { label: 'Banana', value: 'banana' },
  { label: 'Cherry', value: 'cherry' },
]
</script>

<template>
  <BaseSelect
    v-model="selectedFruit"
    :options="fruits"
    label="Choose your favorite fruit"
  />
</template>
```

## Props API

<ArgTypes of={BaseSelectStories} />

## Common Patterns

### Form Validation

<Canvas of={BaseSelectStories.WithError} />

Use the `error` prop to display validation messages:

```vue
<BaseSelect
  v-model="value"
  :options="options"
  label="Required Field"
  :error="validationError"
  required
/>
```

### Pre-selected Values

<Canvas of={BaseSelectStories.WithSelectedValue} />

Set initial values using v-model:

```vue
<BaseSelect
  v-model="selectedValue"
  :options="options"
  label="Pre-selected Option"
/>
```

### Numeric Values

<Canvas of={BaseSelectStories.NumberValues} />

The component handles numeric values seamlessly:

```vue
<BaseSelect
  v-model="selectedNumber"
  :options="[
    { label: 'One', value: 1 },
    { label: 'Two', value: 2 },
    { label: 'Three', value: 3 },
  ]"
  label="Pick a number"
/>
```

### Non-clearable Select

<Canvas of={BaseSelectStories.NotClearable} />

Disable the clear functionality when needed:

```vue
<BaseSelect
  v-model="value"
  :options="options"
  :clearable="false"
  label="Cannot be cleared"
/>
```

## Interactive Examples

### Real-time Value Updates

<Canvas of={BaseSelectStories.Interactive} />

### Complete Form Example

<Canvas of={BaseSelectStories.FormValidation} />

## Styling

The component uses CSS custom properties for theming. Key variables include:

```css
.base-select {
  --color-bg-3: /* Background color */ --color-border: /* Border color */
    --color-text: /* Text color */ --color-text-secondary:
    /* Secondary text color */ --color-primary: /* Focus/active color */
    --color-primary-alpha: /* Focus shadow color */ --radius-sm:
    /* Border radius */;
}
```

## Accessibility

The BaseSelect component follows WCAG 2.1 AA guidelines:

- ✅ Proper semantic HTML structure
- ✅ Keyboard navigation (Tab, Enter, Arrow keys)
- ✅ Screen reader support
- ✅ High contrast support
- ✅ Focus management
- ✅ Error announcement

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## Related Components

- `BaseInput` - For text input fields
- `BaseButton` - For form submission
- `BaseCheckbox` - For boolean selections
- `BaseRadio` - For single selection from visible options

## Migration Guide

### From HTML Select

```diff
- <select v-model="value">
-   <option value="apple">Apple</option>
-   <option value="banana">Banana</option>
- </select>

+ <BaseSelect
+   v-model="value"
+   :options="[
+     { label: 'Apple', value: 'apple' },
+     { label: 'Banana', value: 'banana' }
+   ]"
+ />
```

### From Vue 2 Components

```diff
- <custom-select
-   :value="value"
-   @input="value = $event"
-   :items="options"
- />

+ <BaseSelect
+   v-model="value"
+   :options="options"
+ />
```
