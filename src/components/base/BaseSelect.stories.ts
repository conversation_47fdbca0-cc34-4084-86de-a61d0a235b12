import type { Meta, StoryObj } from '@storybook/vue3-vite'
import { ref } from 'vue'
import BaseSelect from '@/components/base/BaseSelect.vue'

// Sample options for stories
const fruitOptions = [
  { label: 'Apple', value: 'apple' },
  { label: 'Banana', value: 'banana' },
  { label: 'Cherry', value: 'cherry' },
  { label: 'Date', value: 'date' },
  { label: 'Elderberry', value: 'elderberry' },
]

const countryOptions = [
  { label: 'United States', value: 'us' },
  { label: 'Canada', value: 'ca' },
  { label: 'United Kingdom', value: 'uk' },
  { label: 'Australia', value: 'au' },
  { label: 'Germany', value: 'de' },
]

const numberOptions = [
  { label: 'One', value: 1 },
  { label: 'Two', value: 2 },
  { label: 'Three', value: 3 },
  { label: 'Four', value: 4 },
  { label: 'Five', value: 5 },
]

const meta: Meta<typeof BaseSelect> = {
  title: 'Base/BaseSelect',
  component: BaseSelect,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# BaseSelect

A customizable select dropdown component that provides a consistent interface for selecting from a list of options.

## Features

- **Accessible**: Built with semantic HTML and proper ARIA attributes
- **Clearable**: Optional clear button to reset selection
- **Validation**: Built-in error state display
- **Flexible Options**: Supports both string and numeric values
- **Consistent Styling**: Uses design system tokens for consistent appearance

## Usage

\`\`\`vue
<BaseSelect
  v-model="selectedValue"
  :options="options"
  label="Choose an option"
  :error="errorMessage"
  required
/>
\`\`\`

## Props

The component accepts the following props:

- **modelValue**: The currently selected value
- **options**: Array of option objects with \`label\` and \`value\` properties
- **label**: Optional label text displayed above the select
- **error**: Optional error message displayed below the select
- **required**: Whether the field is required
- **clearable**: Whether to show a clear button (default: true)

## Events

- **update:modelValue**: Emitted when the selection changes
        `,
      },
      canvas: {
        sourceState: 'shown',
      },
    },
  },
  argTypes: {
    id: {
      control: 'text',
      description: 'Unique identifier for the select element',
    },
    modelValue: {
      control: 'text',
      description: 'The selected value',
    },
    options: {
      control: 'object',
      description: 'Array of options with label and value properties',
    },
    label: {
      control: 'text',
      description: 'Label text displayed above the select',
    },
    error: {
      control: 'text',
      description: 'Error message displayed below the select',
    },
    required: {
      control: 'boolean',
      description: 'Whether the select is required',
    },
    clearable: {
      control: 'boolean',
      description: 'Whether the select can be cleared',
    },
    'onUpdate:modelValue': {
      action: 'update:modelValue',
      description: 'Emitted when the selected value changes',
    },
  },
  args: {
    id: 'base-select',
    clearable: true,
    required: false,
    options: fruitOptions,
    label: 'Choose an option',
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'The basic select component with a label and list of options. No value is pre-selected.',
      },
    },
  },
  args: {
    id: 'default-select',
    options: fruitOptions,
    label: 'Choose a fruit',
    modelValue: '',
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref(args.modelValue || '')
      return {
        args,
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

export const WithSelectedValue: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'A select component with a pre-selected value. Shows how the component displays when a value is already chosen.',
      },
    },
  },
  args: {
    id: 'selected-value-select',
    options: fruitOptions,
    label: 'Favorite fruit',
    modelValue: 'banana',
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref(args.modelValue || '')
      return {
        args,
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

export const Required: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'A required select field. The `required` prop adds the HTML required attribute for form validation.',
      },
    },
  },
  args: {
    id: 'required-select',
    options: countryOptions,
    label: 'Country (Required)',
    required: true,
    modelValue: '',
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref(args.modelValue || '')
      return {
        args,
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

export const WithError: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Shows the error state with a red error message displayed below the select. Used for validation feedback.',
      },
    },
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref('')
      return {
        args: {
          ...args,
          id: 'error-select',
          options: fruitOptions,
          label: 'Choose a fruit',
          error: 'Please select a valid option',
        },
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

export const NotClearable: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'A select with `clearable: false`. The clear button will not appear even when a value is selected.',
      },
    },
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref('apple')
      return {
        args: {
          ...args,
          id: 'not-clearable-select',
          options: fruitOptions,
          label: 'Choose a fruit',
          clearable: false,
        },
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

export const NumberValues: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Demonstrates using numeric values in options. The component handles both string and number values correctly.',
      },
    },
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref(3)
      return {
        args: {
          ...args,
          id: 'number-select',
          options: numberOptions,
          label: 'Pick a number',
        },
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

export const NoLabel: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'A select without a label. Useful when the select is part of a larger form or when the context makes the purpose clear.',
      },
    },
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref('')
      return {
        args: { ...args, id: 'no-label-select', options: fruitOptions },
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

export const EmptyOptions: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Edge case showing how the component behaves with an empty options array. The select will be empty but still functional.',
      },
    },
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const modelValue = ref('')
      return {
        args: {
          ...args,
          id: 'empty-options-select',
          options: [],
          label: 'No options available',
        },
        modelValue,
        onUpdateValue: (value: any) => {
          modelValue.value = value
        },
      }
    },
    template: `
      <BaseSelect
        v-bind="args"
        :model-value="modelValue"
        @update:model-value="onUpdateValue"
      />
    `,
  }),
}

// Interactive story with reactive state
export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'An interactive example showing real-time value updates. The selected value is displayed below the component to demonstrate the reactive behavior.',
      },
    },
  },
  args: {
    id: 'interactive-select',
    options: fruitOptions,
    label: 'Interactive select',
  },
  render: args => ({
    components: { BaseSelect },
    setup() {
      const selectedValue = ref('')

      return {
        args,
        selectedValue,
        onUpdateValue: (value: any) => {
          selectedValue.value = value
        },
      }
    },
    template: `
      <div style="width: 300px;">
        <BaseSelect
          v-bind="args"
          :model-value="selectedValue"
          @update:model-value="onUpdateValue"
        />
        <p style="margin-top: 1rem; font-size: 0.9rem; color: #666;">
          Selected value: {{ selectedValue || 'None' }}
        </p>
      </div>
    `,
  }),
}

// Form validation example
export const FormValidation: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'A comprehensive form validation example showing multiple BaseSelect components with error handling. Click "Validate Form" to see validation in action.',
      },
    },
  },
  render: () => ({
    components: { BaseSelect },
    setup() {
      const formData = ref({
        fruit: '',
        country: '',
        number: null,
      })

      const errors = ref({
        fruit: '',
        country: '',
        number: '',
      })

      const validateForm = () => {
        errors.value = {
          fruit: !formData.value.fruit ? 'Please select a fruit' : '',
          country: !formData.value.country ? 'Please select a country' : '',
          number:
            formData.value.number === null ? 'Please select a number' : '',
        }
      }

      return {
        formData,
        errors,
        validateForm,
        fruitOptions,
        countryOptions,
        numberOptions,
      }
    },
    template: `
      <div style="width: 400px; display: flex; flex-direction: column; gap: 1rem;">
        <h3>Form Validation Example</h3>

        <BaseSelect
          id="form-fruit-select"
          v-model="formData.fruit"
          :options="fruitOptions"
          label="Favorite Fruit"
          :error="errors.fruit"
          required
        />

        <BaseSelect
          id="form-country-select"
          v-model="formData.country"
          :options="countryOptions"
          label="Country"
          :error="errors.country"
          required
        />

        <BaseSelect
          id="form-number-select"
          v-model="formData.number"
          :options="numberOptions"
          label="Lucky Number"
          :error="errors.number"
          required
        />

        <button
          @click="validateForm"
          style="padding: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;"
        >
          Validate Form
        </button>

        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; font-size: 0.9rem;">
          <strong>Form Data:</strong>
          <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
      </div>
    `,
  }),
}
